import { semanticSearch, searchSimilar, COLLECTIONS } from './qdrant.server';
import { searchCustomers } from './customer.service';
import { searchDevices } from './device.service';
import { searchServiceOrders } from './service-order.service';
import { searchNotes } from './note.service';

/**
 * Perform a semantic search across all collections or a specific collection
 * 
 * @param query The search query
 * @param collection Optional collection to search in
 * @param limit Maximum number of results to return
 * @returns Search results
 */
export async function performSemanticSearch(
  query: string,
  collection?: string,
  limit: number = 10
) {
  try {
    if (!query) {
      return { success: true, data: [] };
    }
    
    let results;
    
    if (collection && collection !== 'all') {
      // Search in a specific collection
      results = await searchSimilar(collection, query, limit);
      results = results.map(result => ({
        ...result,
        collection,
      }));
    } else {
      // Search across all collections
      results = await semanticSearch(query, limit);
    }
    
    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error('Error performing semantic search:', error);
    return {
      success: false,
      error: `Failed to perform semantic search: ${(error as Error).message}`,
      data: [],
    };
  }
}

/**
 * Perform a traditional search across all entity types or a specific type
 * 
 * @param query The search query
 * @param userId The user ID
 * @param entityType Optional entity type to search in
 * @param limit Maximum number of results to return
 * @returns Search results
 */
export async function performTraditionalSearch(
  query: string,
  userId: string,
  entityType?: string,
  limit: number = 10
) {
  try {
    if (!query) {
      return { success: true, data: [] };
    }
    
    let results: any[] = [];
    
    // Determine which entities to search based on entityType
    const searchCustomersPromise = !entityType || entityType === 'customers' || entityType === 'all'
      ? searchCustomers(query, userId, limit)
      : Promise.resolve({ success: true, data: [] });
      
    const searchDevicesPromise = !entityType || entityType === 'devices' || entityType === 'all'
      ? searchDevices(query, userId, limit)
      : Promise.resolve({ success: true, data: [] });
      
    const searchServiceOrdersPromise = !entityType || entityType === 'service_orders' || entityType === 'all'
      ? searchServiceOrders(query, userId, limit)
      : Promise.resolve({ success: true, data: [] });
      
    const searchNotesPromise = !entityType || entityType === 'notes' || entityType === 'all'
      ? searchNotes(query, userId, limit)
      : Promise.resolve({ success: true, data: [] });
    
    // Execute searches in parallel
    const [customersResponse, devicesResponse, serviceOrdersResponse, notesResponse] = await Promise.all([
      searchCustomersPromise,
      searchDevicesPromise,
      searchServiceOrdersPromise,
      searchNotesPromise,
    ]);
    
    // Format results to match semantic search format
    if (customersResponse.success && customersResponse.data) {
      results = results.concat(
        customersResponse.data.map(customer => ({
          id: customer.id,
          score: 1.0, // No real score for traditional search
          collection: COLLECTIONS.CUSTOMERS,
          payload: {
            id: customer.id,
            name: customer.name,
            email: customer.email,
            phone: customer.phone,
            address: customer.address,
            city: customer.city,
            postalCode: customer.postalCode,
            country: customer.country,
          },
        }))
      );
    }
    
    if (devicesResponse.success && devicesResponse.data) {
      results = results.concat(
        devicesResponse.data.map(device => ({
          id: device.id,
          score: 1.0,
          collection: COLLECTIONS.DEVICES,
          payload: {
            id: device.id,
            name: device.name,
            model: device.model,
            serialNumber: device.serialNumber,
            manufacturer: device.manufacturer,
            customerId: device.customerId,
            customerName: device.customer?.name,
          },
        }))
      );
    }
    
    if (serviceOrdersResponse.success && serviceOrdersResponse.data) {
      results = results.concat(
        serviceOrdersResponse.data.map(order => ({
          id: order.id,
          score: 1.0,
          collection: COLLECTIONS.SERVICE_ORDERS,
          payload: {
            id: order.id,
            title: order.title,
            description: order.description,
            status: order.status,
            customerId: order.customerId,
            customerName: order.customer?.name,
            deviceId: order.deviceId,
            deviceName: order.device?.name,
          },
        }))
      );
    }
    
    if (notesResponse.success && notesResponse.data) {
      results = results.concat(
        notesResponse.data.map(note => ({
          id: note.id,
          score: 1.0,
          collection: COLLECTIONS.NOTES,
          payload: {
            id: note.id,
            title: note.title,
            body: note.body,
            userId: note.userId,
            userEmail: note.user?.email,
          },
        }))
      );
    }
    
    // Limit results
    results = results.slice(0, limit);
    
    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error('Error performing traditional search:', error);
    return {
      success: false,
      error: `Failed to perform traditional search: ${(error as Error).message}`,
      data: [],
    };
  }
}

/**
 * Unified search function that can perform either semantic or traditional search
 * 
 * @param query The search query
 * @param userId The user ID
 * @param options Search options
 * @returns Search results
 */
export async function unifiedSearch(
  query: string,
  userId: string,
  options: {
    useSemanticSearch?: boolean;
    collection?: string;
    limit?: number;
  } = {}
) {
  const { useSemanticSearch = true, collection, limit = 10 } = options;
  
  try {
    if (!query) {
      return { success: true, data: [] };
    }
    
    if (useSemanticSearch) {
      return performSemanticSearch(query, collection, limit);
    } else {
      return performTraditionalSearch(query, userId, collection, limit);
    }
  } catch (error) {
    console.error('Error performing unified search:', error);
    return {
      success: false,
      error: `Failed to perform search: ${(error as Error).message}`,
      data: [],
    };
  }
}

// Re-export functions from qdrant.server for convenience
export { searchSimilar, COLLECTIONS } from './qdrant.server';