{"name": "hvac-remix", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build && node patch-build.js", "dev": "remix dev -c \"npm run dev:serve\"", "dev:serve": "NODE_PATH=./app NODE_OPTIONS=\"--import ./mocks/index.js\" remix-serve ./build/index.js", "format": "prettier --write .", "format:repo": "npm run format && npm run lint -- --fix", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint --cache --cache-location ./node_modules/.cache/eslint . --fix", "setup": "prisma generate && prisma migrate deploy && prisma db seed", "start": "remix-serve ./build/index.js", "start:mocks": "NODE_PATH=./app NODE_OPTIONS=\"--import ./mocks/index.js\" remix-serve ./build/index.js", "test": "vitest", "test:e2e:dev": "start-server-and-test dev http://localhost:3000 \"npx cypress open\"", "pretest:e2e:run": "npm run build", "test:e2e:run": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run\"", "test:e2e:customers": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/customers/**/*.cy.ts'\"", "test:e2e:devices": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/devices/**/*.cy.ts'\"", "test:e2e:service-orders": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/service-orders/**/*.cy.ts'\"", "test:e2e:calendar": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/calendar/**/*.cy.ts'\"", "test:e2e:search": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/search/**/*.cy.ts'\"", "test:e2e:ocr": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ocr/**/*.cy.ts'\"", "test:e2e:predictive": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/predictive-maintenance/**/*.cy.ts'\"", "test:e2e:auth": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/auth/**/*.cy.ts'\"", "test:e2e:dashboard": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/dashboard/**/*.cy.ts'\"", "test:e2e:ai": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ai/**/*.cy.ts'\"", "typecheck": "tsc && tsc -p cypress", "validate": "npm-run-all --parallel \"test -- --run\" lint typecheck test:e2e:run", "generate:og-images": "node scripts/generate-og-images.js", "analyze": "source-map-explorer 'build/client/**/*.js'", "analyze:bundle": "node scripts/analyze-bundle.js", "postbuild:disabled": "remix-pwa build", "prod:build": "NODE_ENV=production npm run clean && NODE_ENV=production npm run build", "clean": "rimraf ./build ./public/build", "db:optimize": "node scripts/optimize-database.js", "db:setup": "node scripts/setup-supabase.js", "db:migrate": "node scripts/migrate-data-to-supabase.js", "db:reset": "node scripts/reset-supabase.js", "preproduction": "npm run db:migrate && npm run db:optimize", "deploy:production": "bash scripts/deploy-production.sh", "backup:db": "node scripts/backup-database.js", "security:check": "node scripts/security-check.js", "security:audit": "npm audit --production", "security:fix": "npm audit fix --production", "prod:optimize": "node scripts/production-optimize.js", "prod:analyze": "npm run prod:build && npm run analyze:bundle", "prod:test-load": "node scripts/load-testing.js", "prod:monitor-setup": "node scripts/setup-monitoring.js", "prod:readiness": "node scripts/production-readiness.js", "docs:generate": "node scripts/generate-docs.js", "docs:serve": "npx serve docs", "docs:build": "npm run docs:generate && npx markdown-to-html --input docs --output docs-html", "docs:deploy": "npm run docs:build && npx gh-pages -d docs-html"}, "eslintIgnore": ["/node_modules", "/build", "/public/build"], "dependencies": {"@apollo/client": "^3.13.8", "@azure/cognitiveservices-computervision": "^8.2.0", "@azure/identity": "^4.10.0", "@azure/ms-rest-js": "^2.7.0", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@graphql-tools/schema": "^10.0.23", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@microsoft/microsoft-graph-client": "^3.0.7", "@qdrant/js-client-rest": "^1.7.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@remix-pwa/sw": "^3.0.10", "@remix-pwa/sync": "^3.0.5", "@remix-pwa/worker-runtime": "^2.1.4", "@remix-run/css-bundle": "^2.16.7", "@remix-run/node": "^2.16.7", "@remix-run/react": "^2.16.7", "@remix-run/serve": "^2.16.7", "@sentry/browser": "^7.120.3", "@sentry/node": "^7.120.3", "@sentry/profiling-node": "^1.3.5", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@supabase/supabase-js": "^2.39.7", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@udecode/plate": "^24.5.1", "@udecode/plate-basic-elements": "^24.5.1", "@udecode/plate-basic-marks": "^24.5.1", "@udecode/plate-link": "^24.5.1", "@udecode/plate-list": "^24.5.1", "@udecode/plate-table": "^24.5.1", "apollo-server-express": "^3.13.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "express": "^4.18.3", "framer-motion": "^11.0.8", "graphql": "^16.8.1", "gsap": "^3.12.5", "html2canvas": "^1.4.1", "isbot": "^5.1.1", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "openai": "^4.28.4", "qrcode": "^1.5.3", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "recharts": "^2.12.2", "redis": "^4.6.13", "remix-pwa": "^4.0.5", "slate": "^0.114.0", "slate-dom": "^0.114.0", "slate-history": "^0.113.1", "slate-hyperscript": "^0.100.0", "slate-react": "^0.114.2", "speakeasy": "^2.0.0", "stripe": "^14.19.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.1", "uuid": "^9.0.1", "workbox-background-sync": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@remix-pwa/dev": "^3.1.0", "@remix-run/dev": "^2.16.7", "@remix-run/eslint-config": "^2.16.7", "@tailwindcss/postcss": "^4.1.7", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^6.4.2", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8.56.5", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@types/redis": "^4.0.11", "@types/sqlite3": "^3.1.11", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.3.1", "autoprefixer": "^10.4.17", "cookie": "^0.6.0", "cross-env": "^7.0.3", "cypress": "^13.6.6", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "happy-dom": "^13.3.8", "msw": "^2.2.2", "npm-run-all2": "^6.1.1", "postcss": "^8.4.35", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "rimraf": "^5.0.5", "source-map-explorer": "^2.5.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "start-server-and-test": "^2.0.3", "tsx": "^4.7.1", "typescript": "^5.3.3", "vite": "^5.1.4", "vite-tsconfig-paths": "^4.3.1", "vitest": "^1.3.1"}, "engines": {"node": ">=18.0.0"}, "supabase": {"schema": "supabase/schema.sql"}}